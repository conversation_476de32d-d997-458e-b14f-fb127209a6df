Originally created on the EDIFY-2018 PC using Powershell 7.5.1


	1. ps2exe was already installed by using the following code:
	------>  Install-Module -Name ps2exe -Scope CurrentUser


	2. Then I ran Powershell from the directory that contained my ps1 script (originally named OrganizeFilesByDate.ps1) that I wanted to convert to exe and ran the following code:
	------> ps2exe '.\CREATE - List of Files in Directory.ps1' -noConsole -noOutput -title "List My Files" -product "List My Files" -copyright "Copyright (c) 2025, Adam <PERSON>" -iconFile "PanAura.ico" -version "2.8.1"


	3. Additional things can be done like applying icon to the exe file during conversion using the ps2exe and so to learn more you can using the following code for help:
	------> Get-Help Invoke-ps2exe -Full
	   or simply type:
	------> ps2exe -?


Note:

The ps1 file will not exclude itself from the list and so if you run just the ps1 in powershell then the text file will show the ps1 in the list.  This is because I intended to use the exe version instead of the ps1 and therefore
when you run the exe version the script will successfully remove the exe from the file list.

Be sure to place the icon in the same location as the ps1 file when converting it to the exe (this will prevent it from being blocked by antivirus software)



